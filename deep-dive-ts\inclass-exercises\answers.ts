// 01
export type CustomerMessage = {
  content: string;
  source: "chat" | "email" | "unknown";
};

export function parseCustomerMessage(input: unknown): CustomerMessage {
  if (typeof input === "string") {
    return {
      content: input,
      source: "email",
    };
  }

  if (Array.isArray(input)) {
    return {
      content: input.join("\n"),
      source: "chat",
    };
  }

  return {
    content: "",
    source: "unknown",
  };
}

// 02
type PositiveSentiment = "happy" | "satisfied";
type NegativeSentiment = "dissatisfied" | "angry";

export type Sentiment = PositiveSentiment | NegativeSentiment;
export type Response = { message: string; notify: <PERSON>olean };

export function respondToSentiment(sentiment: Sentiment): Response {
  if (sentiment === "happy" || sentiment === "satisfied") {
    return handlePositiveSentiment(sentiment);
  }

  if (sentiment === "dissatisfied" || sentiment === "angry") {
    return handleNegativeSentiment(sentiment);
  }

  return { message: "We don't understand.", notify: true };
}

function handlePositiveSentiment(sentiment: PositiveSentiment): Response {
  const response = {
    message: "",
    notify: false,
  };

  if (sentiment === "happy") {
    response.message = "Hooray!";
  } else if (sentiment === "satisfied") {
    response.message = "We are glad.";
  }

  return response;
}

function handleNegativeSentiment(sentiment: NegativeSentiment): Response {
  const response = {
    message: "",
    notify: false,
  };

  if (sentiment === "dissatisfied") {
    response.message = "We are sorry.";
  } else if (sentiment === "angry") {
    response.message = "We apologize. A manager will contact you.";
    response.notify = true;
  }

  return response;
}

// 03
type SkippityModel = {
  name: "Skippity";
  version: string;
  search: boolean;
};

type JeanModel = {
  name: "Jean";
  version: string;
  think: boolean;
};

export type Model = SkippityModel | JeanModel;

function isModelSkippity(model: Model): model is SkippityModel {
  return model.name === "Skippity";
}

function isModelJean(model: Model): model is JeanModel {
  return model.name === "Jean";
}

export function activateModel(model: Model): string {
  if (isModelSkippity(model)) {
    const searchStatus = model.search ? "enabled" : "disabled";
    return `Activated model Skippity version ${model.version} with searching ${searchStatus}`;
  } else if (isModelJean(model)) {
    const thinkStatus = model.think ? "enabled" : "disabled";
    return `Activated model Jean version ${model.version} with thinking ${thinkStatus}`;
  }

  // This should never happen due to exhaustive type checking, but TypeScript requires a return
  return "Unknown model type";
}

// 04
export type Topic = "question" | "complaint" | "upgrade" | "refund";

type Chat = {
  topic: Topic;
  userId: string;
};

type CountReport = {
  questions: number;
  complaints: number;
  upgrades: number;
  refunds: number;
};

export function countComplaints(chats: Chat[]): CountReport {
  let counts = { questions: 0, complaints: 0, upgrades: 0, refunds: 0 };
  for (const chat of chats) {
    counts = incrementCount(chat, counts);
  }
  return counts;
}

function incrementCount(chat: Chat, counts: CountReport): CountReport {
  switch (chat.topic) {
    case "question":
      counts.questions++;
      return counts;
    case "complaint":
      counts.complaints++;
      return counts;
    case "refund":
      counts.refunds++;
      return counts;
    case "upgrade":
      counts.upgrades++;
      return counts;
  }
}

// 05
export type UserFeedback = {
  email?: string;
  rating?: number;
};

export function handleFeedback(feedback: UserFeedback) {
  if (!feedback.rating || isValidRating(feedback.rating) === false) {
    return "Give a rating between 1 and 5.";
  }

  if (!feedback.email || !feedback.email.includes("@")) {
    return "Provide a valid email address.";
  }

  return `Thanks, ${getEmailUsername(feedback.email)}! Rating: ${ratingToString(feedback.rating)}`;
}

function getEmailUsername(email: string): string {
  const atIndex = email.indexOf("@");
  return atIndex !== -1 ? email.slice(0, atIndex) : email;
}

function isValidRating(rating: number): rating is 1 | 2 | 3 | 4 | 5 {
  return [1, 2, 3, 4, 5].includes(rating);
}

function ratingToString(rating: 1 | 2 | 3 | 4 | 5): string {
  switch (rating) {
    case 1:
      return "Very Bad";
    case 2:
      return "Bad";
    case 3:
      return "Average";
    case 4:
      return "Good";
    case 5:
      return "Excellent";
  }
}
