// Exercise: Type Narrowing with Assertion Functions in TypeScript

// Question 1:
// Write an assertion function `assertIsNumber` that takes a parameter
// `value` of type `any` and throws an error if the value is not a number.
// Use this function to narrow the type of a variable.

// Question 2:
// Write an assertion function `assertIsArray` that takes a parameter
// `value` of type `any` and throws an error if the value is not an array.
// Use this function to narrow the type of a variable.
