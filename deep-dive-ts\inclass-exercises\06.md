# Exercise-06

## Overview

Fix the compile-time bug in the `updateUser` function. It should accept a `User` object, but where each property is optional.

## Requirements

**1.** Fix the compile-time bug in the `updateUser` function so it accepts a `User` object where each property is optional.

### Code
```ts
export interface User {
  id: string;
  email: string;
}

export function updateUser(user: User) {
  if (user.id) {
    return "can't update id";
  }
  if (user.email) {
    return `updating email to ${user.email}`;
  }
  return "nothing to update";
}
``` 