// Exercise 1: Generic Interface for a Key-Value Pair
// Define a generic interface `KeyValuePair` that accepts two types: `K` for the key and `V` for the value.
// Then, create a function `printKeyValuePair` that accepts an object of type `KeyValuePair`
// and logs the key and value.

// Test the function:
// const stringNumberPair: KeyValuePair<string, number> = {
//   key: "age",
//   value: 30,
// };

// const numberBooleanPair: KeyValuePair<number, boolean> = {
//   key: 1,
//   value: true,
// };

// printKeyValuePair(stringNumberPair); // Output: Key: age, Value: 30
// printKeyValuePair(numberBooleanPair); // Output: Key: 1, Value: true
