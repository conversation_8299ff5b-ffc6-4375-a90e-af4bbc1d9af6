# Exercise-02

## Overview

Support.ai needs a sentiment response system that can analyze customer sentiment and provide appropriate responses. The system handles positive sentiments ("happy", "satisfied") and negative sentiments ("dissatisfied", "angry") differently.

## Task

Complete three functions that work together to handle customer sentiment analysis and generate appropriate responses.

```typescript
type PositiveSentiment = "happy" | "satisfied";
type NegativeSentiment = "dissatisfied" | "angry";

export type Sentiment = PositiveSentiment | NegativeSentiment;
export type Response = { message: string; notify: boolean };

export function respondToSentiment(sentiment: Sentiment): Response {
  // Your implementation here
}

function handlePositiveSentiment(sentiment: PositiveSentiment): Response {
  // Your implementation here
}

function handleNegativeSentiment(sentiment: NegativeSentiment): Response {
  // Your implementation here
}
```

## Requirements

**1.** Complete the `respondToSentiment` function:
   - If the input is positive, pass it to `handlePositiveSentiment` and return the result
   - If the input is negative, pass it to `handleNegativeSentiment` and return the result
   - If the input is anything else:
     - Return the message "We don't understand."
     - *Notify* the manager

**2.** Complete the `handlePositiveSentiment` function:
   - If the input is "happy":
     - The response message should be "Hooray!"
     - Don't notify the manager
   - If the input is "satisfied":
     - The response message should be "We are glad."
     - Don't notify the manager

**3.** Complete the `handleNegativeSentiment` function:
   - If the input is "dissatisfied":
     - The response message should be "We are sorry."
     - Don't notify the manager
   - If the input is "angry":
     - The response message should be "We apologize. A manager will contact you."
     - *Notify* the manager

### Code
```ts
type PositiveSentiment = "happy" | "satisfied";
type NegativeSentiment = "dissatisfied" | "angry";

export type Sentiment = PositiveSentiment | NegativeSentiment;
export type Response = { message: string; notify: boolean };

export function respondToSentiment(sentiment: Sentiment): Response {
  // ?
}

function handlePositiveSentiment(sentiment: PositiveSentiment): Response {
  // ?
}

function handleNegativeSentiment(sentiment: NegativeSentiment): Response {
  // ?
}
```
 