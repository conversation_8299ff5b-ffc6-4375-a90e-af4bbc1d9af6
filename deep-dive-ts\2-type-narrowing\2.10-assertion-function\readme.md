Problem: You want to narrow a type by asserting that one or more conditions are true.

Solution: Use an assertion function. The return signature of an assertion function `asserts` a condition.

```ts
function assertIsString(value: any): asserts value is string {
  if (typeof value !== "string") {
    throw new Error("Not a string!");
  }
}
```

```ts
// Assertions can be more complex and contain multiple checks
function assertIsStringWithAtLeast3Chars(value: any): asserts value is string {
  if (typeof value !== "string") {
    throw new Error("Not a string!");
  }
  if (value.length < 3) {
    throw new Error("String too short!");
  }
}

const myString = "12";
assertIsStringWithAtLeast3Chars(myString); // Runtime error: String too short!
```

## Type Predicate vs Assertion Function

| Aspect | Type Predicate | Assertion Function |
|--------|----------------|-------------------|
| **Return Type** | `boolean` | `void` |
| **Syntax** | `value is Type` | `asserts value is Type` |
| **Error Handling** | Returns false | Throws error |
| **Usage** | Conditional logic | Direct validation |

### Examples:

**Type Predicate** - Handle both cases:
```ts
function isString(value: any): value is string {
  return typeof value === "string";
}

const process = (input: unknown) => {
  if (isString(input)) {
    return input.toUpperCase(); // Safe to use string methods
  }
  return "Invalid input"; // Handle non-string case
};
```

**Assertion Function** - Fail fast:
```ts
function assertIsString(value: any): asserts value is string {
  if (typeof value !== "string") {
    throw new Error("Not a string!");
  }
}

const process = (input: unknown) => {
  assertIsString(input); // Throws if not string
  return input.toUpperCase(); // TypeScript knows it's string here
};
```

### When to Use:

- **Type Predicate**: When you need conditional logic and want to handle both valid/invalid cases gracefully
- **Assertion Function**: When you expect the value to be a specific type and want to fail fast if it's not
