# Exercise-03

## Overview

We support multiple LLMs, those per-token prices aren't gonna arbitrage themselves...

Anyhow, we need to write code to activate a specific model when starting a chat with a customer.

## Requirements

**1.** Fix `isModelSkippity`: it should be a type predicate function.

**2.** Fix `isModelJean` by also making it a type predicate function.

**3.** Complete the `activateModel` function:
   - If the given model is Skippity, return the string `Activated model Skippity version {VERSION} with searching {STATUS}`
     - `{VERSION}` is simply the `version` property
     - `{STATUS}` is either `enabled` or `disabled` based on the `search` property
   - If the given model is Jean, return the string `Activated model Jean version {VERSION} with thinking {STATUS}`
     - `{VERSION}` is simply the `version` property
     - `{STATUS}` is either `enabled` or `disabled` based on the `think` property

### Code
```ts
type SkippityModel = {
  name: "Skippity";
  version: string;
  search: boolean;
};

type JeanModel = {
  name: "<PERSON>";
  version: string;
  think: boolean;
};

export type Model = SkippityModel | JeanModel;

function isModelSkippity(model: Model): boolean {
  return model.name === "Skippity";
}

function isModelJean(model: Model): boolean {
  return model.name === "Jean";
}

export function activateModel(model: Model): string {
  // ?
}
``` 