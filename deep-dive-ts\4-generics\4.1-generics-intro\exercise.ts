// Exercise: Understanding Functions with a Generic Argument

// Question 1:
// Write a generic function named `identity` that takes a single argument of type `T` and returns it.
// The function should work with any type.

// Question 2:
// Call `identity` with an explicit type argument of `string` and the value "Hi".
// What is the type of the returned value?

// Question 3:
// Remove the explicit type argument. What is the type of the returned value now?

// Question 4:
// Why is it useful to use generics in the `identity` function instead of using `any`?

// Question 5:
// Create a generic function called `wrapInArray` that takes a value of any type and returns
// it wrapped in an array. Ensure the return type is an array of the same type as the input.
