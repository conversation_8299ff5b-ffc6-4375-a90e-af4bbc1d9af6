// Returns a function that takes a name and returns their title.
function parseNames<const T extends { name: string; title: string }>(
  names: T[]
) {
  return (name: T["name"]) => names.find((n) => n.name === name)?.title;
}

const getTitleByName = parseNames([
  {
    name: "<PERSON>",
    title: "Author",
  },
  {
    name: "<PERSON>",
    title: "Editor",
  },
]);

// Note autocomplete support when using const modifier
const title = getTitleByName("<PERSON>");
