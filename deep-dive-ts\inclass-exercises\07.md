# Exercise-07

## Overview

Our users' emails and phone numbers are usually optional but become required when it's time to make a purchase.

Complete the `addBillingInfo` function.

## Requirements

**1.** Use `Required` to enforce that the `ContactInfo` properties are required.

**2.** Return the string `billing info: {EMAIL}, {PHONE}`, replacing `{<PERSON><PERSON><PERSON>}` `{PHONE}` with the respective fields.

### Code
```ts
export interface ContactInfo {
  email?: string;
  phoneNumber?: string;
}

export function addBillingInfo(info: ContactInfo) {
  // ?
}
``` 