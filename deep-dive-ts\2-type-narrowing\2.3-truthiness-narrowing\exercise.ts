// Exercise: Truthiness Narrowing in TypeScript

// Question 1:
// Write a function `printIfTruthy` that takes a single argument `value` of type `unknown`.
// The function should print "Truthy" if the value is truthy, and "Falsy" if the value is falsy.

// Question 2:
// Write a function `isNonEmptyArray` that takes a single argument `value` of type `unknown`.
// The function should return true if the value is an array with at least one element, and false otherwise.
