# Who is this course for?

So who is this course for? Well, we're going to dive deep on TypeScript. So this course is for developers who already understand TypeScript basics like primitive types, type aliases, interfaces, modules, and how to type functions and classes. If you're new to TypeScript, watch my intro course, "TypeScript: Getting Started" first.

If you're ready to go beyond the basics and become one of the best TypeScript developers at your company, this course is for you.
