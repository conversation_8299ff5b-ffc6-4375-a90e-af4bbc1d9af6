// Default to any if no type is specified
interface Store<T = any> {
  state: T;
  setState: (newState: T) => void;
}

// Default usage (accepts any state)
const genericStore: Store = { state: { user: "<PERSON>" }, setState: () => {} };

// Typed usage
const userStore: Store<{ name: string; age: number }> = {
  state: { name: "<PERSON>", age: 30 },
  setState: (newState) => console.log(newState),
};
userStore.setState({ name: "<PERSON>", age: 25 }); // Output: { name: '<PERSON>', age: 25 }
