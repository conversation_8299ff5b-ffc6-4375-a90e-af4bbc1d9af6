# Exercise-08

## Overview

Run the code as-is and notice the compiler error, then fix the bug.

## Requirements

**1.** Run the code as-is and notice the compiler error.

**2.** Fix the bug in `getStatusMessage()` function.

### Code
```ts
export type ModelStatus = "waiting" | "thinking" | "responding";

const waitingMessage = "Awaiting prompt";
const thinkingMessage = "Cooking";
const respondingMessage = "Sending response";

export function getStatusMessage(status: ModelStatus) {
  const map: Record<ModelStatus, string> = {
    waiting: waitingMessage,
    thinking: thinkingMessage,
  };

  return map[status];
} 