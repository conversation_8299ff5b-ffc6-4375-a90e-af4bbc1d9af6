// Exercise: The `keyof` type operator

// Question 1:
// Given the following interface, use the `keyof` operator to create a union type
// that represents the `Book` interface's keys.
interface Book {
  title: string;
  author: string;
  publishedYear: number;
}

// Question 2:
// Create a function `getProperty` that takes an object of type `Book`
// and a key of type `<PERSON><PERSON><PERSON>`, and returns the value of the specified key.

// Example usage:
// const book: Book = {
//   title: "Code Complete",
//   author: "<PERSON>",
//   publishedYear: 1993,
// };

// const name = getProperty(book, "author"); // "<PERSON>"
