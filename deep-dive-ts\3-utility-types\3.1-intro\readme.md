# Utility Types Intro

Sometimes you want to **create a new type from an existing type**. In these cases, consider using **utility types**.

In "TypeScript: Getting Started", we used our first utility type, **`Record`**. Record creates a type for storing **key value pairs**, also known as a **dictionary**. But TypeScript provides **several other utility types** that help you manipulate types. They are **generic types** that take one or more type arguments and return a **new type**.

We'll explore **generics** in detail later in the course, but for now all you need to understand is a **generic type** is a type that **accepts one or more types as arguments**.
