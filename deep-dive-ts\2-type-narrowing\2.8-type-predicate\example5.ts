// 2 Examples of Union Types with Type Predicates

// Example 1: Animal Union (Cat | Dog)
interface Cat {
  name: string;
  numLives: number;
}

interface Dog {
  name: string;
  breed: string;
}

type Animal = Cat | Dog;

const isCat = (animal: Animal): animal is Cat => {
  return (animal as Cat).numLives !== undefined;
};

const makeAnimalSound = (animal: Animal) => {
  if (isCat(animal)) {
    return `${animal.name} meows and has ${animal.numLives} lives`;
  } else {
    return `${animal.name} barks and is a ${animal.breed}`;
  }
};

// Example 2: Tagged Union (Discriminated Union)
interface LoadingState {
  status: "loading";
}

interface SuccessState {
  status: "success";
  data: string[];
}

interface ErrorState {
  status: "error";
  message: string;
}

type ApiState = LoadingState | SuccessState | ErrorState;

const isLoading = (state: ApiState): state is LoadingState => {
  return state.status === "loading";
};

const isSuccess = (state: ApiState): state is SuccessState => {
  return state.status === "success";
};

const isError = (state: ApiState): state is ErrorState => {
  return state.status === "error";
};

const handleApiState = (state: ApiState) => {
  if (isLoading(state)) {
    return "Loading...";
  } else if (isSuccess(state)) {
    return `Loaded ${state.data.length} items`; // TypeScript knows data exists
  } else if (isError(state)) {
    return `Error: ${state.message}`; // TypeScript knows message exists
  }
};

// Usage Examples
console.log("=== Example 1: Animal Union ===");
const cat: Cat = { name: "Whiskers", numLives: 9 };
const dog: Dog = { name: "Rex", breed: "German Shepherd" };
console.log(makeAnimalSound(cat)); // Whiskers meows and has 9 lives
console.log(makeAnimalSound(dog)); // Rex barks and is a German Shepherd

console.log("\n=== Example 2: Tagged Union ===");
const loadingState: LoadingState = { status: "loading" };
const successState: SuccessState = {
  status: "success",
  data: ["item1", "item2"],
};
const errorState: ErrorState = { status: "error", message: "Network failed" };

console.log(handleApiState(loadingState)); // Loading...
console.log(handleApiState(successState)); // Loaded 2 items
console.log(handleApiState(errorState)); // Error: Network failed
