# Exercise-10

## Overview

The Support.ai data engineering team needs to prepare LLM training data by creating arrays of loosely-structured pairs.

## Requirements

**1.** Complete the `pair` function. It takes two arrays:
   - One with inputs of type A
   - One with outputs of type B

**2.** Return a list of training pairs (tuples) `[A, B]`, one for each index until the shorter list runs out.

For example, training data for people's favorite databases:

- A: `["lane", "hunter", "allan", "dan"]`
- B: `["postgres", "mysql", "db.txt", "roll-your-own"]`
- Output: `[["lane", "postgres"], ["hunter", "mysql"], ["allan", "db.txt"], ["dan", "roll-your-own"]]`

### Code
```ts
export function pair<A, B>(a: A[], b: B[]): [A, B][] {
  return [];
}
``` 