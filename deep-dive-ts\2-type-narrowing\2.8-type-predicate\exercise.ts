// Exercise: Type Narrowing with Type Predicates in TypeScript

// Question 1:
// Define a type predicate function `isNumber` that takes a parameter of type `unknown`
// and returns `true` if the parameter is a number, and `false` otherwise.

// Question 2:
// Use the `isNumber` type predicate function to narrow the type of the variable typed
// to `unknown` below and log `value` + 1.

const value: unknown = 42;

// Question 3:
// Define a type predicate function `isArray` that takes a parameter of type `unknown`
// and returns `true` if the parameter is an array, and `false` otherwise.

// Question 4:
// Use the `isArray` type predicate function to narrow the type of `arrayExample` below
// then log the first array element.

const arrayExample: unknown = [1, 2, 3];
