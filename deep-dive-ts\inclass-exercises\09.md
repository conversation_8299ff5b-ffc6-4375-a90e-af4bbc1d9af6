# Exercise-09

## Overview

Fix the bug in the `UserWithoutID` type so that it has the 3 properties it needs.

## Requirements

**1.** Fix the bug in the `UserWithoutID` type so that it has the 3 properties it needs.

### Code
```ts
export interface User {
  id: string;
  name: string;
  email: string;
  age: number;
}

export type UserWithoutID = Pick<User, "name">;

export function stripID(user: User): UserWithoutID {
  const { name, email, age } = user;
  return { name, email, age };
}
``` 