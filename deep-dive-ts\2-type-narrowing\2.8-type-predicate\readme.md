# Narrowing via a Type Predicate

Problem: You want to conveniently narrow a type based on a condition, and you want to centralize the narrowing logic to support reuse.

Solution: Create your own type predicate to narrow a type. This is also called a user-defined type guard.

## Type Predicate Solution

```ts
function isCat(animal: Cat | Dog): animal is Cat {
  return (animal as Cat).numLives !== undefined;
}

function makeNoise(animal: Cat | Dog): string {
  if (isCat(animal)) {
    // TypeScript now knows animal is Cat
    return `${animal.name} meows and has ${animal.numLives} lives`;
  } else {
    // TypeScript now knows animal is Dog
    return `${animal.name} barks and is a ${animal.breed}`;
  }
}

// Now you can reuse the type predicate everywhere
const cats = animals.filter(isCat); // TypeScript knows cats is Cat[]
cats.forEach(cat => {
  console.log(`${cat.name} has ${cat.numLives} lives`); // No type errors!
});
```


