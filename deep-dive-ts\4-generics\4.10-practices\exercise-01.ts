const phoneList = [
  { customerId: "0001", areaCode: "321", num: "123-4566" },
  { customerId: "0002", areaCode: "174", num: "142-3626" },
  { customerId: "0003", areaCode: "192", num: "012-7190" },
  { customerId: "0005", areaCode: "402", num: "652-5782" },
  { customerId: "0004", areaCode: "301", num: "184-8501" },
];

// Array: linear search: O(n) = n, Add: O(n)

// hash table:
// phoneDict['005'] O(1)
const phoneDict = {
  "0001": {
    customerId: "0001",
    areaCode: "321",
    num: "123-4566",
  },
  "0002": {
    customerId: "0002",
    areaCode: "174",
    num: "142-3626",
  },
  /*... and so on */
};

interface PhoneInfo {
  customerId: string;
  areaCode: string;
  num: string;
}

// =============================== Usage Example ===============================

function listToDict(
  list: PhoneInfo[], // take the list as an argument
  idGen: (arg: PhoneInfo) => string // a callback to get Ids
): { [k: string]: PhoneInfo } {
  const dict: { [k: string]: PhoneInfo } = {};

  // Loop through the array
  list.forEach((element) => {
    const dictKey = idGen(element);
    dict[dictKey] = element; // store element under key
  });

  // return the dictionary
  return dict;
}

const result = listToDict(phoneList, (item) => item.customerId);

listToDict(
  [
    new Date("10-01-2021"),
    new Date("03-14-2021"),
    new Date("06-03-2021"),
    new Date("09-30-2021"),
    new Date("02-17-2021"),
    new Date("05-21-2021"),
  ],
  (arg) => arg.toISOString()
);

// ==== Problem Example ====

// const dict = listToDict(
//   [{ name: "William" }, { name: "Cao" }],
//   (item) => item.name
// );

// => {
//   'William': {
//     name: "William"
//   },
//   "Cao": {
//     name: "Cao"
//   }
// }
