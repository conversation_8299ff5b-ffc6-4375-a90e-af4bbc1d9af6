# What You'll Learn

In this course, we'll cover some of TypeScript's most powerful and useful features.

I'll share over a dozen different ways to safely narrow types, and how to choose between them.

We'll use TypeScript's built-in utility types to generate new types, and explore how they're written to help us understand how we can create our own utilities.

We'll explore generics, which allow us to write code that works with many different types while still maintaining type safety.

We'll use mapped types to create new types from existing types using advanced features like string literal types, conditional types, looping via in, remapping via as, and infer.

Then, we'll practice our newfound skills by learning how to recreate some of TypeScript's built-in utility types, such as <PERSON>ial, Required, Readonly, Record, and Pick.

TypeScript doesn't exist at compile time, so we'll explore how to implement runtime validation schemas to validate and strongly type runtime data including URL parameters, environment variables, form inputs, and API responses. And I'll show how to infer your TypeScript types from runtime schemas so you can enjoy both compile-time and runtime safety.

You'll learn how to implement and safely type JavaScript decorators for handling cross-cutting concerns like logging, validation, and error handling.

Then, I'll share a variety of advanced TypeScript patterns and tips that I'm sure you'll find useful, including how to implement loose autocomplete, named tuples, branded types, excess properties, symbols, and how to use JSDocs to add types to plain JS files.

Configuring TypeScript properly is intimidating because there are over 100 tsconfig settings, so I'll share a number of suggestions for configuring TypeScript effectively including optimizing strictness, performance, debugging, and tips for migrating your JavaScript to TypeScript.

Finally, I'll close out the course with recommended next steps including linting, error handling tips, generating types, and resources for practicing your newfound skills.
